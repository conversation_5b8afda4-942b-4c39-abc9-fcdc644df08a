using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IFC.Domain.Entities.POS;

/// <summary>
/// POS Settings entity - maps to POSSetting table in POS database
/// Replicates VB.NET table structure exactly:
/// CREATE TABLE [POSSetting] ([Ser] [int] NULL, [Company_IdPOS] [int] NULL,
/// [Company_NamePOS] [nvarchar](150) NULL, [Brand_IdPOS] [int] NULL,
/// [Brand_NamePOS] [nvarchar](150) NULL, [CostCenter_IdPOS] [int] NULL,
/// [CostCenter_NamePOS] [nvarchar](150) NULL, [IsDelete] [bit] NULL)
/// </summary>
[Table("POSSetting")]
public class POSSetting
{
    /// <summary>
    /// Primary key - maps to [Ser] column in original VB.NET table
    /// </summary>
    [Key]
    public int POSSettingId { get; set; }

    /// <summary>
    /// Company ID in POS system
    /// </summary>
    public int CompanyIdPOS { get; set; }

    /// <summary>
    /// Company Name in POS system - max length 200 to match IFCDbContext
    /// </summary>
    [MaxLength(200)]
    public string CompanyNamePOS { get; set; } = string.Empty;

    /// <summary>
    /// Brand ID in POS system
    /// </summary>
    public int BrandIdPOS { get; set; }

    /// <summary>
    /// Brand Name in POS system - max length 200 to match IFCDbContext
    /// </summary>
    [MaxLength(200)]
    public string BrandNamePOS { get; set; } = string.Empty;

    /// <summary>
    /// Cost Center ID in POS system
    /// </summary>
    public int CostCenterIdPOS { get; set; }

    /// <summary>
    /// Cost Center Name in POS system - max length 200 to match IFCDbContext
    /// </summary>
    [MaxLength(200)]
    public string CostCenterNamePOS { get; set; } = string.Empty;

    /// <summary>
    /// IsActive flag - converted from IsDelete in IFCDbContext
    /// IsDelete=0 means IsActive=true as per IFCDbContext configuration
    /// </summary>
    public bool IsActive { get; set; } = true;
}
