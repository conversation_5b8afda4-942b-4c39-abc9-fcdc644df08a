{"version": 3, "names": ["pathsCache", "exports", "path", "WeakMap", "scope", "clear", "clear<PERSON>ath", "clearScope", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent", "parentPath", "get", "getOrCreateCachedPaths", "node", "paths", "set", "Map"], "sources": ["../src/cache.ts"], "sourcesContent": ["import type { Node } from \"@babel/types\";\nimport type NodePath from \"./path/index.ts\";\nimport type Scope from \"./scope/index.ts\";\n\nlet pathsCache: WeakMap<Node, Map<Node, NodePath>> = new WeakMap();\nexport { pathsCache as path };\nexport let scope: WeakMap<Node, Scope> = new WeakMap();\n\nexport function clear() {\n  clearPath();\n  clearScope();\n}\n\nexport function clearPath() {\n  pathsCache = new WeakMap();\n}\n\nexport function clearScope() {\n  scope = new WeakMap();\n}\n\nexport function getCachedPaths(path: NodePath) {\n  const { parent, parentPath } = path;\n  return process.env.BABEL_8_BREAKING && parentPath\n    ? parentPath._store\n    : pathsCache.get(parent);\n}\n\nexport function getOrCreateCachedPaths(node: Node, parentPath?: NodePath) {\n  if (process.env.BABEL_8_BREAKING && parentPath) {\n    return (parentPath._store ||= new Map());\n  }\n\n  let paths = pathsCache.get(node);\n  if (!paths) pathsCache.set(node, (paths = new Map()));\n\n  return paths;\n}\n"], "mappings": ";;;;;;;;;;;AAIA,IAAIA,UAA8C,GAAAC,OAAA,CAAAC,IAAA,GAAG,IAAIC,OAAO,CAAC,CAAC;AAE3D,IAAIC,KAA2B,GAAAH,OAAA,CAAAG,KAAA,GAAG,IAAID,OAAO,CAAC,CAAC;AAE/C,SAASE,KAAKA,CAAA,EAAG;EACtBC,SAAS,CAAC,CAAC;EACXC,UAAU,CAAC,CAAC;AACd;AAEO,SAASD,SAASA,CAAA,EAAG;EAC1BL,OAAA,CAAAC,IAAA,GAAAF,UAAU,GAAG,IAAIG,OAAO,CAAC,CAAC;AAC5B;AAEO,SAASI,UAAUA,CAAA,EAAG;EAC3BN,OAAA,CAAAG,KAAA,GAAAA,KAAK,GAAG,IAAID,OAAO,CAAC,CAAC;AACvB;AAEO,SAASK,cAAcA,CAACN,IAAc,EAAE;EAC7C,MAAM;IAAEO,MAAM;IAAEC;EAAW,CAAC,GAAGR,IAAI;EACnC,OAEIF,UAAU,CAACW,GAAG,CAACF,MAAM,CAAC;AAC5B;AAEO,SAASG,sBAAsBA,CAACC,IAAU,EAAEH,UAAqB,EAAE;EAAA;EAKxE,IAAII,KAAK,GAAGd,UAAU,CAACW,GAAG,CAACE,IAAI,CAAC;EAChC,IAAI,CAACC,KAAK,EAAEd,UAAU,CAACe,GAAG,CAACF,IAAI,EAAGC,KAAK,GAAG,IAAIE,GAAG,CAAC,CAAE,CAAC;EAErD,OAAOF,KAAK;AACd", "ignoreList": []}