-- Create POSSetting table in POS database if it doesn't exist
-- This script replicates the VB.NET table creation logic

USE [Tiba_Pos]
GO

-- Check if POSSetting table exists, if not create it
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[POSSetting]') AND type in (N'U'))
BEGIN
    -- Create POSSetting table exactly as defined in VB.NET IFC_Main_Frm.vb
    CREATE TABLE [dbo].[POSSetting] (
        [Ser] [int] NULL,
        [Company_IdPOS] [int] NULL,
        [Company_NamePOS] [nvarchar](150) NULL,
        [Brand_IdPOS] [int] NULL,
        [Brand_NamePOS] [nvarchar](150) NULL,
        [CostCenter_IdPOS] [int] NULL,
        [CostCenter_NamePOS] [nvarchar](150) NULL,
        [IsDelete] [bit] NULL
    ) ON [PRIMARY]

    -- Add default constraint for IsDelete
    ALTER TABLE [dbo].[POSSetting] ADD CONSTRAINT [DF_POSSetting_IsDelete] DEFAULT ((0)) FOR [IsDelete]

    PRINT 'POSSetting table created successfully in POS database'
END
ELSE
BEGIN
    PRINT 'POSSetting table already exists in POS database'
END
GO

-- Check if Sales_POS table exists, if not create it
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Sales_POS]') AND type in (N'U'))
BEGIN
    -- Create Sales_POS table exactly as defined in VB.NET IFC_Main_Frm.vb
    CREATE TABLE [dbo].[Sales_POS] (
        [Ser] [int] IDENTITY(1,1) NOT NULL,
        [CostCenterPOS_Id] [int] NULL,
        [CostCenterPOS_Name] [nvarchar](150) NULL,
        [CostCenter_Id_To] [int] NULL,
        [CostCenter_Name_To] [nvarchar](150) NULL,
        [Transaction_Code] [int] NULL,
        [Product_Id] [int] NULL,
        [Product_Code] [nvarchar](50) NULL,
        [Product_Name] [nvarchar](150) NULL,
        [Reciving_Q] [float] NULL,
        [Cost_Product] [float] NULL,
        [CostTotalLine] [float] NULL,
        [Patch_Ser] [int] NULL,
        [Patch_Name] [nvarchar](150) NULL,
        [Unt_Id] [int] NULL,
        [Unt_Name] [nvarchar](50) NULL,
        [Transaction_Date_Create] [datetime] NULL,
        [Transaction_Date_Update] [datetime] NULL,
        [Is_Production] [bit] NULL,
        [CompanyPOS_Id] [int] NULL,
        [CompanyPOS_Name] [nvarchar](150) NULL,
        [OutLetPOS_Id] [int] NULL,
        [OutLetPOS_Name] [nvarchar](150) NULL,
        [MethodOfPayment_Id] [int] NULL,
        [MethodOfPayment_Name] [nvarchar](150) NULL,
        [Sales_Price] [float] NULL,
        [IsExpire] [bit] NULL,
        [ProductionCode] [int] NULL,
        [ChDID] [int] NULL
    ) ON [PRIMARY]

    -- Add default constraint for ChDID
    ALTER TABLE [dbo].[Sales_POS] ADD CONSTRAINT [DF_Sales_POS_ChDID] DEFAULT ((0)) FOR [ChDID]

    PRINT 'Sales_POS table created successfully in POS database'
END
ELSE
BEGIN
    PRINT 'Sales_POS table already exists in POS database'
END
GO

-- Check if StockOnHandTbl_POS table exists, if not create it
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[StockOnHandTbl_POS]') AND type in (N'U'))
BEGIN
    -- Create StockOnHandTbl_POS table exactly as defined in VB.NET IFC_Main_Frm.vb
    CREATE TABLE [dbo].[StockOnHandTbl_POS] (
        [Ser] [int] IDENTITY(1,1) NOT NULL,
        [Product_Id] [int] NULL,
        [Product_Name] [nvarchar](150) NULL,
        [Product_Code] [nvarchar](50) NULL,
        [CostCenter_Id] [int] NULL,
        [Quntity] [decimal](24, 4) NULL
    ) ON [PRIMARY]

    PRINT 'StockOnHandTbl_POS table created successfully in POS database'
END
ELSE
BEGIN
    PRINT 'StockOnHandTbl_POS table already exists in POS database'
END
GO

PRINT 'POS database table creation script completed'
