{"name": "reflect-metadata", "version": "0.2.2", "description": "Polyfill for Metadata Reflection API", "type": "commonjs", "main": "Reflect.js", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "default": "./Reflect.js"}, "./lite": {"types": "./index.d.ts", "default": "./ReflectLite.js"}, "./no-conflict": {"types": "./no-conflict.d.ts", "default": "./ReflectNoConflict.js"}, "./Reflect": "./Reflect.js", "./Reflect.js": "./Reflect.js"}, "scripts": {"prepublishOnly": "gulp prepublish", "build": "gulp build", "test": "gulp test", "start": "gulp start"}, "repository": {"type": "git", "url": "https://github.com/rbuckton/reflect-metadata.git"}, "keywords": ["decorator", "metadata", "javascript", "reflect"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/rbuckton"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/rbuckton/reflect-metadata/issues"}, "homepage": "http://rbuckton.github.io/reflect-metadata", "dependencies": {}, "devDependencies": {"@types/chai": "^3.4.34", "@types/mocha": "^2.2.34", "@types/node": "^10.17.60", "chai": "^3.5.0", "del": "^2.2.2", "ecmarkup": "^3.9.3", "gulp": "^3.9.1", "gulp-emu": "^1.1.0", "gulp-live-server": "0.0.30", "gulp-mocha": "^3.0.1", "gulp-rename": "^1.2.2", "gulp-sequence": "^0.4.6", "gulp-tsb": "^2.0.3", "mocha": "^3.2.0", "typescript": "^2.1.4"}}