.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, rgba(0, 0, 0, 0.1))}.cdk-high-contrast-active .mat-ripple-element{display:none}.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border:var(--mat-focus-indicator-border-width, 3px) var(--mat-focus-indicator-border-style, solid) var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}.cdk-high-contrast-active{--mat-focus-indicator-display: block}.mat-mdc-focus-indicator{position:relative}.mat-mdc-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-mdc-focus-indicator-display, none);border:var(--mat-mdc-focus-indicator-border-width, 3px) var(--mat-mdc-focus-indicator-border-style, solid) var(--mat-mdc-focus-indicator-border-color, transparent);border-radius:var(--mat-mdc-focus-indicator-border-radius, 4px)}.mat-mdc-focus-indicator:focus::before{content:""}.cdk-high-contrast-active{--mat-mdc-focus-indicator-display: block}.mat-app-background{background-color:var(--mat-app-background-color, transparent);color:var(--mat-app-text-color, inherit)}html{--mat-ripple-color:rgba(255, 255, 255, 0.1)}html{--mat-option-selected-state-label-text-color:#c2185b;--mat-option-label-text-color:white;--mat-option-hover-state-layer-color:rgba(255, 255, 255, 0.08);--mat-option-focus-state-layer-color:rgba(255, 255, 255, 0.08);--mat-option-selected-state-layer-color:rgba(255, 255, 255, 0.08)}.mat-accent{--mat-option-selected-state-label-text-color:#b0bec5;--mat-option-label-text-color:white;--mat-option-hover-state-layer-color:rgba(255, 255, 255, 0.08);--mat-option-focus-state-layer-color:rgba(255, 255, 255, 0.08);--mat-option-selected-state-layer-color:rgba(255, 255, 255, 0.08)}.mat-warn{--mat-option-selected-state-label-text-color:#f44336;--mat-option-label-text-color:white;--mat-option-hover-state-layer-color:rgba(255, 255, 255, 0.08);--mat-option-focus-state-layer-color:rgba(255, 255, 255, 0.08);--mat-option-selected-state-layer-color:rgba(255, 255, 255, 0.08)}html{--mat-optgroup-label-text-color:white}.mat-primary{--mat-full-pseudo-checkbox-selected-icon-color:#c2185b;--mat-full-pseudo-checkbox-selected-checkmark-color:#303030;--mat-full-pseudo-checkbox-unselected-icon-color:rgba(255, 255, 255, 0.7);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color:#303030;--mat-full-pseudo-checkbox-disabled-unselected-icon-color:#686868;--mat-full-pseudo-checkbox-disabled-selected-icon-color:#686868;--mat-minimal-pseudo-checkbox-selected-checkmark-color:#c2185b;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color:#686868}html{--mat-full-pseudo-checkbox-selected-icon-color:#b0bec5;--mat-full-pseudo-checkbox-selected-checkmark-color:#303030;--mat-full-pseudo-checkbox-unselected-icon-color:rgba(255, 255, 255, 0.7);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color:#303030;--mat-full-pseudo-checkbox-disabled-unselected-icon-color:#686868;--mat-full-pseudo-checkbox-disabled-selected-icon-color:#686868;--mat-minimal-pseudo-checkbox-selected-checkmark-color:#b0bec5;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color:#686868}.mat-accent{--mat-full-pseudo-checkbox-selected-icon-color:#b0bec5;--mat-full-pseudo-checkbox-selected-checkmark-color:#303030;--mat-full-pseudo-checkbox-unselected-icon-color:rgba(255, 255, 255, 0.7);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color:#303030;--mat-full-pseudo-checkbox-disabled-unselected-icon-color:#686868;--mat-full-pseudo-checkbox-disabled-selected-icon-color:#686868;--mat-minimal-pseudo-checkbox-selected-checkmark-color:#b0bec5;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color:#686868}.mat-warn{--mat-full-pseudo-checkbox-selected-icon-color:#f44336;--mat-full-pseudo-checkbox-selected-checkmark-color:#303030;--mat-full-pseudo-checkbox-unselected-icon-color:rgba(255, 255, 255, 0.7);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color:#303030;--mat-full-pseudo-checkbox-disabled-unselected-icon-color:#686868;--mat-full-pseudo-checkbox-disabled-selected-icon-color:#686868;--mat-minimal-pseudo-checkbox-selected-checkmark-color:#f44336;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color:#686868}html{--mat-app-background-color:#303030;--mat-app-text-color:white}.mat-elevation-z0,.mat-mdc-elevation-specific.mat-elevation-z0{box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z1,.mat-mdc-elevation-specific.mat-elevation-z1{box-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z2,.mat-mdc-elevation-specific.mat-elevation-z2{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z3,.mat-mdc-elevation-specific.mat-elevation-z3{box-shadow:0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z4,.mat-mdc-elevation-specific.mat-elevation-z4{box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z5,.mat-mdc-elevation-specific.mat-elevation-z5{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 5px 8px 0px rgba(0, 0, 0, 0.14), 0px 1px 14px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z6,.mat-mdc-elevation-specific.mat-elevation-z6{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-elevation-z7,.mat-mdc-elevation-specific.mat-elevation-z7{box-shadow:0px 4px 5px -2px rgba(0, 0, 0, 0.2), 0px 7px 10px 1px rgba(0, 0, 0, 0.14), 0px 2px 16px 1px rgba(0, 0, 0, 0.12)}.mat-elevation-z8,.mat-mdc-elevation-specific.mat-elevation-z8{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z9,.mat-mdc-elevation-specific.mat-elevation-z9{box-shadow:0px 5px 6px -3px rgba(0, 0, 0, 0.2), 0px 9px 12px 1px rgba(0, 0, 0, 0.14), 0px 3px 16px 2px rgba(0, 0, 0, 0.12)}.mat-elevation-z10,.mat-mdc-elevation-specific.mat-elevation-z10{box-shadow:0px 6px 6px -3px rgba(0, 0, 0, 0.2), 0px 10px 14px 1px rgba(0, 0, 0, 0.14), 0px 4px 18px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z11,.mat-mdc-elevation-specific.mat-elevation-z11{box-shadow:0px 6px 7px -4px rgba(0, 0, 0, 0.2), 0px 11px 15px 1px rgba(0, 0, 0, 0.14), 0px 4px 20px 3px rgba(0, 0, 0, 0.12)}.mat-elevation-z12,.mat-mdc-elevation-specific.mat-elevation-z12{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z13,.mat-mdc-elevation-specific.mat-elevation-z13{box-shadow:0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 13px 19px 2px rgba(0, 0, 0, 0.14), 0px 5px 24px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z14,.mat-mdc-elevation-specific.mat-elevation-z14{box-shadow:0px 7px 9px -4px rgba(0, 0, 0, 0.2), 0px 14px 21px 2px rgba(0, 0, 0, 0.14), 0px 5px 26px 4px rgba(0, 0, 0, 0.12)}.mat-elevation-z15,.mat-mdc-elevation-specific.mat-elevation-z15{box-shadow:0px 8px 9px -5px rgba(0, 0, 0, 0.2), 0px 15px 22px 2px rgba(0, 0, 0, 0.14), 0px 6px 28px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z16,.mat-mdc-elevation-specific.mat-elevation-z16{box-shadow:0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z17,.mat-mdc-elevation-specific.mat-elevation-z17{box-shadow:0px 8px 11px -5px rgba(0, 0, 0, 0.2), 0px 17px 26px 2px rgba(0, 0, 0, 0.14), 0px 6px 32px 5px rgba(0, 0, 0, 0.12)}.mat-elevation-z18,.mat-mdc-elevation-specific.mat-elevation-z18{box-shadow:0px 9px 11px -5px rgba(0, 0, 0, 0.2), 0px 18px 28px 2px rgba(0, 0, 0, 0.14), 0px 7px 34px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z19,.mat-mdc-elevation-specific.mat-elevation-z19{box-shadow:0px 9px 12px -6px rgba(0, 0, 0, 0.2), 0px 19px 29px 2px rgba(0, 0, 0, 0.14), 0px 7px 36px 6px rgba(0, 0, 0, 0.12)}.mat-elevation-z20,.mat-mdc-elevation-specific.mat-elevation-z20{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 20px 31px 3px rgba(0, 0, 0, 0.14), 0px 8px 38px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z21,.mat-mdc-elevation-specific.mat-elevation-z21{box-shadow:0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 21px 33px 3px rgba(0, 0, 0, 0.14), 0px 8px 40px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z22,.mat-mdc-elevation-specific.mat-elevation-z22{box-shadow:0px 10px 14px -6px rgba(0, 0, 0, 0.2), 0px 22px 35px 3px rgba(0, 0, 0, 0.14), 0px 8px 42px 7px rgba(0, 0, 0, 0.12)}.mat-elevation-z23,.mat-mdc-elevation-specific.mat-elevation-z23{box-shadow:0px 11px 14px -7px rgba(0, 0, 0, 0.2), 0px 23px 36px 3px rgba(0, 0, 0, 0.14), 0px 9px 44px 8px rgba(0, 0, 0, 0.12)}.mat-elevation-z24,.mat-mdc-elevation-specific.mat-elevation-z24{box-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12)}.mat-theme-loaded-marker{display:none}html{--mat-option-label-text-font:Roboto, sans-serif;--mat-option-label-text-line-height:24px;--mat-option-label-text-size:16px;--mat-option-label-text-tracking:0.03125em;--mat-option-label-text-weight:400}html{--mat-optgroup-label-text-font:Roboto, sans-serif;--mat-optgroup-label-text-line-height:24px;--mat-optgroup-label-text-size:16px;--mat-optgroup-label-text-tracking:0.03125em;--mat-optgroup-label-text-weight:400}html{--mdc-elevated-card-container-shape:4px;--mdc-outlined-card-container-shape:4px;--mdc-outlined-card-outline-width:1px}html{--mdc-elevated-card-container-color:#424242;--mdc-elevated-card-container-elevation:0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-outlined-card-container-color:#424242;--mdc-outlined-card-outline-color:rgba(255, 255, 255, 0.12);--mdc-outlined-card-container-elevation:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-card-subtitle-text-color:rgba(255, 255, 255, 0.7)}html{--mat-card-title-text-font:Roboto, sans-serif;--mat-card-title-text-line-height:32px;--mat-card-title-text-size:20px;--mat-card-title-text-tracking:0.0125em;--mat-card-title-text-weight:500;--mat-card-subtitle-text-font:Roboto, sans-serif;--mat-card-subtitle-text-line-height:22px;--mat-card-subtitle-text-size:14px;--mat-card-subtitle-text-tracking:0.0071428571em;--mat-card-subtitle-text-weight:500}html{--mdc-linear-progress-active-indicator-height:4px;--mdc-linear-progress-track-height:4px;--mdc-linear-progress-track-shape:0}.mat-mdc-progress-bar{--mdc-linear-progress-active-indicator-color:#c2185b;--mdc-linear-progress-track-color:rgba(194, 24, 91, 0.25)}.mat-mdc-progress-bar.mat-accent{--mdc-linear-progress-active-indicator-color:#b0bec5;--mdc-linear-progress-track-color:rgba(176, 190, 197, 0.25)}.mat-mdc-progress-bar.mat-warn{--mdc-linear-progress-active-indicator-color:#f44336;--mdc-linear-progress-track-color:rgba(244, 67, 54, 0.25)}html{--mdc-plain-tooltip-container-shape:4px;--mdc-plain-tooltip-supporting-text-line-height:16px}html{--mdc-plain-tooltip-container-color:#616161;--mdc-plain-tooltip-supporting-text-color:#fff}html{--mdc-plain-tooltip-supporting-text-font:Roboto, sans-serif;--mdc-plain-tooltip-supporting-text-size:12px;--mdc-plain-tooltip-supporting-text-weight:400;--mdc-plain-tooltip-supporting-text-tracking:0.0333333333em}html{--mdc-filled-text-field-active-indicator-height:1px;--mdc-filled-text-field-focus-active-indicator-height:2px;--mdc-filled-text-field-container-shape:4px;--mdc-outlined-text-field-outline-width:1px;--mdc-outlined-text-field-focus-outline-width:2px;--mdc-outlined-text-field-container-shape:4px}html{--mdc-filled-text-field-caret-color:#c2185b;--mdc-filled-text-field-focus-active-indicator-color:#c2185b;--mdc-filled-text-field-focus-label-text-color:rgba(194, 24, 91, 0.87);--mdc-filled-text-field-container-color:#4a4a4a;--mdc-filled-text-field-disabled-container-color:#464646;--mdc-filled-text-field-label-text-color:rgba(255, 255, 255, 0.6);--mdc-filled-text-field-hover-label-text-color:rgba(255, 255, 255, 0.6);--mdc-filled-text-field-disabled-label-text-color:rgba(255, 255, 255, 0.38);--mdc-filled-text-field-input-text-color:rgba(255, 255, 255, 0.87);--mdc-filled-text-field-disabled-input-text-color:rgba(255, 255, 255, 0.38);--mdc-filled-text-field-input-text-placeholder-color:rgba(255, 255, 255, 0.6);--mdc-filled-text-field-error-hover-label-text-color:#f44336;--mdc-filled-text-field-error-focus-label-text-color:#f44336;--mdc-filled-text-field-error-label-text-color:#f44336;--mdc-filled-text-field-error-caret-color:#f44336;--mdc-filled-text-field-active-indicator-color:rgba(255, 255, 255, 0.42);--mdc-filled-text-field-disabled-active-indicator-color:rgba(255, 255, 255, 0.06);--mdc-filled-text-field-hover-active-indicator-color:rgba(255, 255, 255, 0.87);--mdc-filled-text-field-error-active-indicator-color:#f44336;--mdc-filled-text-field-error-focus-active-indicator-color:#f44336;--mdc-filled-text-field-error-hover-active-indicator-color:#f44336;--mdc-outlined-text-field-caret-color:#c2185b;--mdc-outlined-text-field-focus-outline-color:#c2185b;--mdc-outlined-text-field-focus-label-text-color:rgba(194, 24, 91, 0.87);--mdc-outlined-text-field-label-text-color:rgba(255, 255, 255, 0.6);--mdc-outlined-text-field-hover-label-text-color:rgba(255, 255, 255, 0.6);--mdc-outlined-text-field-disabled-label-text-color:rgba(255, 255, 255, 0.38);--mdc-outlined-text-field-input-text-color:rgba(255, 255, 255, 0.87);--mdc-outlined-text-field-disabled-input-text-color:rgba(255, 255, 255, 0.38);--mdc-outlined-text-field-input-text-placeholder-color:rgba(255, 255, 255, 0.6);--mdc-outlined-text-field-error-caret-color:#f44336;--mdc-outlined-text-field-error-focus-label-text-color:#f44336;--mdc-outlined-text-field-error-label-text-color:#f44336;--mdc-outlined-text-field-error-hover-label-text-color:#f44336;--mdc-outlined-text-field-outline-color:rgba(255, 255, 255, 0.38);--mdc-outlined-text-field-disabled-outline-color:rgba(255, 255, 255, 0.06);--mdc-outlined-text-field-hover-outline-color:rgba(255, 255, 255, 0.87);--mdc-outlined-text-field-error-focus-outline-color:#f44336;--mdc-outlined-text-field-error-hover-outline-color:#f44336;--mdc-outlined-text-field-error-outline-color:#f44336;--mat-form-field-focus-select-arrow-color:rgba(194, 24, 91, 0.87);--mat-form-field-disabled-input-text-placeholder-color:rgba(255, 255, 255, 0.38);--mat-form-field-state-layer-color:rgba(255, 255, 255, 0.87);--mat-form-field-error-text-color:#f44336;--mat-form-field-select-option-text-color:rgba(0, 0, 0, 0.87);--mat-form-field-select-disabled-option-text-color:rgba(0, 0, 0, 0.38);--mat-form-field-leading-icon-color:unset;--mat-form-field-disabled-leading-icon-color:unset;--mat-form-field-trailing-icon-color:unset;--mat-form-field-disabled-trailing-icon-color:unset;--mat-form-field-error-focus-trailing-icon-color:unset;--mat-form-field-error-hover-trailing-icon-color:unset;--mat-form-field-error-trailing-icon-color:unset;--mat-form-field-enabled-select-arrow-color:rgba(255, 255, 255, 0.54);--mat-form-field-disabled-select-arrow-color:rgba(255, 255, 255, 0.38);--mat-form-field-hover-state-layer-opacity:0.08;--mat-form-field-focus-state-layer-opacity:0.24}.mat-mdc-form-field.mat-accent{--mdc-filled-text-field-caret-color:#b0bec5;--mdc-filled-text-field-focus-active-indicator-color:#b0bec5;--mdc-filled-text-field-focus-label-text-color:rgba(176, 190, 197, 0.87);--mdc-outlined-text-field-caret-color:#b0bec5;--mdc-outlined-text-field-focus-outline-color:#b0bec5;--mdc-outlined-text-field-focus-label-text-color:rgba(176, 190, 197, 0.87);--mat-form-field-focus-select-arrow-color:rgba(176, 190, 197, 0.87)}.mat-mdc-form-field.mat-warn{--mdc-filled-text-field-caret-color:#f44336;--mdc-filled-text-field-focus-active-indicator-color:#f44336;--mdc-filled-text-field-focus-label-text-color:rgba(244, 67, 54, 0.87);--mdc-outlined-text-field-caret-color:#f44336;--mdc-outlined-text-field-focus-outline-color:#f44336;--mdc-outlined-text-field-focus-label-text-color:rgba(244, 67, 54, 0.87);--mat-form-field-focus-select-arrow-color:rgba(244, 67, 54, 0.87)}html{--mat-form-field-container-height:56px;--mat-form-field-filled-label-display:block;--mat-form-field-container-vertical-padding:16px;--mat-form-field-filled-with-label-container-padding-top:24px;--mat-form-field-filled-with-label-container-padding-bottom:8px}html{--mdc-filled-text-field-label-text-font:Roboto, sans-serif;--mdc-filled-text-field-label-text-size:16px;--mdc-filled-text-field-label-text-tracking:0.03125em;--mdc-filled-text-field-label-text-weight:400;--mdc-outlined-text-field-label-text-font:Roboto, sans-serif;--mdc-outlined-text-field-label-text-size:16px;--mdc-outlined-text-field-label-text-tracking:0.03125em;--mdc-outlined-text-field-label-text-weight:400;--mat-form-field-container-text-font:Roboto, sans-serif;--mat-form-field-container-text-line-height:24px;--mat-form-field-container-text-size:16px;--mat-form-field-container-text-tracking:0.03125em;--mat-form-field-container-text-weight:400;--mat-form-field-outlined-label-text-populated-size:16px;--mat-form-field-subscript-text-font:Roboto, sans-serif;--mat-form-field-subscript-text-line-height:20px;--mat-form-field-subscript-text-size:12px;--mat-form-field-subscript-text-tracking:0.0333333333em;--mat-form-field-subscript-text-weight:400}html{--mat-select-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}html{--mat-select-panel-background-color:#424242;--mat-select-enabled-trigger-text-color:rgba(255, 255, 255, 0.87);--mat-select-disabled-trigger-text-color:rgba(255, 255, 255, 0.38);--mat-select-placeholder-text-color:rgba(255, 255, 255, 0.6);--mat-select-enabled-arrow-color:rgba(255, 255, 255, 0.54);--mat-select-disabled-arrow-color:rgba(255, 255, 255, 0.38);--mat-select-focused-arrow-color:rgba(194, 24, 91, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html .mat-mdc-form-field.mat-accent{--mat-select-panel-background-color:#424242;--mat-select-enabled-trigger-text-color:rgba(255, 255, 255, 0.87);--mat-select-disabled-trigger-text-color:rgba(255, 255, 255, 0.38);--mat-select-placeholder-text-color:rgba(255, 255, 255, 0.6);--mat-select-enabled-arrow-color:rgba(255, 255, 255, 0.54);--mat-select-disabled-arrow-color:rgba(255, 255, 255, 0.38);--mat-select-focused-arrow-color:rgba(176, 190, 197, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html .mat-mdc-form-field.mat-warn{--mat-select-panel-background-color:#424242;--mat-select-enabled-trigger-text-color:rgba(255, 255, 255, 0.87);--mat-select-disabled-trigger-text-color:rgba(255, 255, 255, 0.38);--mat-select-placeholder-text-color:rgba(255, 255, 255, 0.6);--mat-select-enabled-arrow-color:rgba(255, 255, 255, 0.54);--mat-select-disabled-arrow-color:rgba(255, 255, 255, 0.38);--mat-select-focused-arrow-color:rgba(244, 67, 54, 0.87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, 0.87)}html{--mat-select-arrow-transform:translateY(-8px)}html{--mat-select-trigger-text-font:Roboto, sans-serif;--mat-select-trigger-text-line-height:24px;--mat-select-trigger-text-size:16px;--mat-select-trigger-text-tracking:0.03125em;--mat-select-trigger-text-weight:400}html{--mat-autocomplete-container-shape:4px;--mat-autocomplete-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)}html{--mat-autocomplete-background-color:#424242}html{--mdc-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mdc-dialog-container-shadow-color:#000;--mdc-dialog-container-shape:4px;--mat-dialog-container-max-width:80vw;--mat-dialog-container-small-max-width:80vw;--mat-dialog-container-min-width:0;--mat-dialog-actions-alignment:start;--mat-dialog-actions-padding:8px;--mat-dialog-content-padding:20px 24px;--mat-dialog-with-actions-content-padding:20px 24px;--mat-dialog-headline-padding:0 24px 9px}html{--mdc-dialog-container-color:#424242;--mdc-dialog-subhead-color:rgba(255, 255, 255, 0.87);--mdc-dialog-supporting-text-color:rgba(255, 255, 255, 0.6)}html{--mdc-dialog-subhead-font:Roboto, sans-serif;--mdc-dialog-subhead-line-height:32px;--mdc-dialog-subhead-size:20px;--mdc-dialog-subhead-weight:500;--mdc-dialog-subhead-tracking:0.0125em;--mdc-dialog-supporting-text-font:Roboto, sans-serif;--mdc-dialog-supporting-text-line-height:24px;--mdc-dialog-supporting-text-size:16px;--mdc-dialog-supporting-text-weight:400;--mdc-dialog-supporting-text-tracking:0.03125em}.mat-mdc-standard-chip{--mdc-chip-container-shape-family:rounded;--mdc-chip-container-shape-radius:16px 16px 16px 16px;--mdc-chip-with-avatar-avatar-shape-family:rounded;--mdc-chip-with-avatar-avatar-shape-radius:14px 14px 14px 14px;--mdc-chip-with-avatar-avatar-size:28px;--mdc-chip-with-icon-icon-size:18px;--mdc-chip-outline-width:0;--mdc-chip-outline-color:transparent;--mdc-chip-disabled-outline-color:transparent;--mdc-chip-focus-outline-color:transparent;--mdc-chip-hover-state-layer-opacity:0.04;--mdc-chip-with-avatar-disabled-avatar-opacity:1;--mdc-chip-flat-selected-outline-width:0;--mdc-chip-selected-hover-state-layer-opacity:0.04;--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity:1;--mdc-chip-with-icon-disabled-icon-opacity:1;--mat-chip-disabled-container-opacity:0.4;--mat-chip-trailing-action-opacity:0.54;--mat-chip-trailing-action-focus-opacity:1;--mat-chip-trailing-action-state-layer-color:transparent;--mat-chip-selected-trailing-action-state-layer-color:transparent;--mat-chip-trailing-action-hover-state-layer-opacity:0;--mat-chip-trailing-action-focus-state-layer-opacity:0}.mat-mdc-standard-chip{--mdc-chip-disabled-label-text-color:#fafafa;--mdc-chip-elevated-container-color:#595959;--mdc-chip-elevated-selected-container-color:#595959;--mdc-chip-elevated-disabled-container-color:#595959;--mdc-chip-flat-disabled-selected-container-color:#595959;--mdc-chip-focus-state-layer-color:white;--mdc-chip-hover-state-layer-color:white;--mdc-chip-selected-hover-state-layer-color:white;--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-selected-focus-state-layer-color:white;--mdc-chip-selected-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:#fafafa;--mdc-chip-selected-label-text-color:#fafafa;--mdc-chip-with-icon-icon-color:#fafafa;--mdc-chip-with-icon-disabled-icon-color:#fafafa;--mdc-chip-with-icon-selected-icon-color:#fafafa;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:#fafafa;--mdc-chip-with-trailing-icon-trailing-icon-color:#fafafa;--mat-chip-selected-disabled-trailing-icon-color:#fafafa;--mat-chip-selected-trailing-icon-color:#fafafa}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-primary,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-primary{--mdc-chip-disabled-label-text-color:white;--mdc-chip-elevated-container-color:#c2185b;--mdc-chip-elevated-selected-container-color:#c2185b;--mdc-chip-elevated-disabled-container-color:#c2185b;--mdc-chip-flat-disabled-selected-container-color:#c2185b;--mdc-chip-focus-state-layer-color:white;--mdc-chip-hover-state-layer-color:white;--mdc-chip-selected-hover-state-layer-color:white;--mdc-chip-fo