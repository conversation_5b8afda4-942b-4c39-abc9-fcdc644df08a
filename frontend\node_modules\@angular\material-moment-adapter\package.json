{"name": "@angular/material-moment-adapter", "version": "17.3.10", "description": "Angular Material Moment Adapter", "repository": {"type": "git", "url": "https://github.com/angular/components.git"}, "license": "MIT", "bugs": {"url": "https://github.com/angular/components/issues"}, "homepage": "https://github.com/angular/components#readme", "peerDependencies": {"@angular/material": "17.3.10", "@angular/core": "^17.0.0 || ^18.0.0", "moment": "^2.18.1"}, "dependencies": {"tslib": "^2.3.0"}, "ng-update": {"packageGroup": ["@angular/material", "@angular/cdk", "@angular/material-moment-adapter"]}, "schematics": "./schematics/collection.json", "sideEffects": false, "module": "./fesm2022/material-moment-adapter.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/material-moment-adapter_public_index.mjs", "esm": "./esm2022/material-moment-adapter_public_index.mjs", "default": "./fesm2022/material-moment-adapter.mjs"}}}