"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
function default_1() {
    // Noop schematic so the CLI doesn't throw if users try to `ng add` this package.
    // Also allows us to add more functionality in the future.
    return () => { };
}
exports.default = default_1;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9zcmMvbWF0ZXJpYWwtbW9tZW50LWFkYXB0ZXIvc2NoZW1hdGljcy9uZy1hZGQvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7QUFJSDtJQUNFLGlGQUFpRjtJQUNqRiwwREFBMEQ7SUFDMUQsT0FBTyxHQUFHLEVBQUUsR0FBRSxDQUFDLENBQUM7QUFDbEIsQ0FBQztBQUpELDRCQUlDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7UnVsZX0gZnJvbSAnQGFuZ3VsYXItZGV2a2l0L3NjaGVtYXRpY3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiAoKTogUnVsZSB7XG4gIC8vIE5vb3Agc2NoZW1hdGljIHNvIHRoZSBDTEkgZG9lc24ndCB0aHJvdyBpZiB1c2VycyB0cnkgdG8gYG5nIGFkZGAgdGhpcyBwYWNrYWdlLlxuICAvLyBBbHNvIGFsbG93cyB1cyB0byBhZGQgbW9yZSBmdW5jdGlvbmFsaXR5IGluIHRoZSBmdXR1cmUuXG4gIHJldHVybiAoKSA9PiB7fTtcbn1cbiJdfQ==