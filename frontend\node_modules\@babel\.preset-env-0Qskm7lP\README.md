# @babel/preset-env

> A Babel preset for each environment.

See our website [@babel/preset-env](https://babeljs.io/docs/babel-preset-env) for more information or the [issues](https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20preset-env%22+is%3Aopen) associated with this package.

## Install

Using npm:

```sh
npm install --save-dev @babel/preset-env
```

or using yarn:

```sh
yarn add @babel/preset-env --dev
```
