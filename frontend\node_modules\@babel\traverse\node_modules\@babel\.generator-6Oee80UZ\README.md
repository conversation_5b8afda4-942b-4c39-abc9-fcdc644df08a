# @babel/generator

> Turns an AST into code.

See our website [@babel/generator](https://babeljs.io/docs/babel-generator) for more information or the [issues](https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen) associated with this package.

## Install

Using npm:

```sh
npm install --save-dev @babel/generator
```

or using yarn:

```sh
yarn add @babel/generator --dev
```
