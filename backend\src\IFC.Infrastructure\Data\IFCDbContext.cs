using Microsoft.EntityFrameworkCore;
using IFC.Domain.Entities;
using IFC.Domain.Entities.POS;

namespace IFC.Infrastructure.Data;

/// <summary>
/// Entity Framework DbContext for IFC Production System
/// Maps to the existing VB.NET database structure
/// </summary>
public class IFCDbContext : DbContext
{
    public IFCDbContext(DbContextOptions<IFCDbContext> options) : base(options)
    {
    }

    // Main entities
    // SCM Database entities (Production, Recipes, Inventory)
    public DbSet<Product> Products { get; set; }
    public DbSet<Recipe> Recipes { get; set; }
    public DbSet<RecipeIngredient> RecipeIngredients { get; set; }
    public DbSet<ProductionOrder> ProductionOrders { get; set; }
    public DbSet<ProductionStep> ProductionSteps { get; set; }
    public DbSet<Inventory> Inventories { get; set; }
    public DbSet<InventoryMovement> InventoryMovements { get; set; }
    public DbSet<InventoryBatch> InventoryBatches { get; set; }
    public DbSet<MaterialConsumption> MaterialConsumptions { get; set; }

    // POS entities that are ALSO in SCM database (not separate POS database)
    public DbSet<POSSetting> POSSettings { get; set; }
    public DbSet<SalesPOS> SalesPOS { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Map to existing VB.NET database table names - ALL IN SCM DATABASE (SCMTIBA)
        modelBuilder.Entity<Product>().ToTable("ProductsTbl");

        // CRITICAL FIX: Recipe and RecipeIngredient share the same table in VB.NET
        // Based on VB.NET analysis: Recipe_Product_Id is the discriminator
        // - Recipe_Product_Id IS NULL = Recipe header
        // - Recipe_Product_Id IS NOT NULL = Recipe ingredient
        // We'll use Table Per Hierarchy (TPH) with a discriminator
        modelBuilder.Entity<Recipe>().ToTable("Recipe_ProductsTbl");
        modelBuilder.Entity<RecipeIngredient>().ToTable("Recipe_ProductsTbl");

        modelBuilder.Entity<ProductionOrder>().ToTable("ProductionOrdersTbl");
        modelBuilder.Entity<ProductionStep>().ToTable("ProductionStepsTbl");
        modelBuilder.Entity<Inventory>().ToTable("StockOnHandTbl");
        modelBuilder.Entity<InventoryMovement>().ToTable("InventoryMovementsTbl");
        modelBuilder.Entity<InventoryBatch>().ToTable("Patches");
        modelBuilder.Entity<MaterialConsumption>().ToTable("MaterialConsumptionTbl");

        // CRITICAL: POSSetting and Sales_POS are in SCM database, not POS database!
        // Evidence: Conn.SELECT_TXT("select * from POSSetting") uses SqlConn (SCM)
        modelBuilder.Entity<POSSetting>().ToTable("POSSetting");
        modelBuilder.Entity<SalesPOS>().ToTable("Sales_POS");

        // Configure Product entity to match VB.NET structure
        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.ProductId);
            entity.Property(e => e.ProductId).HasColumnName("Product_Id");
            entity.Property(e => e.ProductCode).HasColumnName("Product_Code").HasMaxLength(50);
            entity.Property(e => e.ProductName).HasColumnName("Product_Name").HasMaxLength(200);
            entity.Property(e => e.BrandName).HasColumnName("Product_BrandName").HasMaxLength(100);
            entity.Property(e => e.UnitId).HasColumnName("Unt_Id");
            entity.Property(e => e.UnitName).HasColumnName("Unt_Name").HasMaxLength(50);
            entity.Property(e => e.UnitGroupId).HasColumnName("Unt_GroupId");
            entity.Property(e => e.UnitQuantity).HasColumnName("Unt_Q").HasColumnType("decimal(18,4)");
            entity.Property(e => e.DepartmentId).HasColumnName("Depart_Id");
            entity.Property(e => e.GroupId).HasColumnName("Group_Id");
            entity.Property(e => e.SubGroupId).HasColumnName("SubGroup_Id");
            entity.Property(e => e.CostPerUnit).HasColumnName("Cost_Product").HasColumnType("decimal(18,4)");
            entity.Property(e => e.AverageCost).HasColumnName("AvCost").HasColumnType("decimal(18,4)");
            entity.Property(e => e.SalesPrice).HasColumnName("SalesPrice").HasColumnType("decimal(18,4)");
            entity.Property(e => e.MinimumStock).HasColumnName("MinStock").HasColumnType("decimal(18,4)");
            entity.Property(e => e.MaximumStock).HasColumnName("MaxStock").HasColumnType("decimal(18,4)");
            entity.Property(e => e.ReorderPoint).HasColumnName("ReOrder").HasColumnType("decimal(18,4)");
            entity.Property(e => e.Notes).HasColumnName("Notes").HasMaxLength(500);
            entity.Property(e => e.IsStock).HasColumnName("IsStock");
            entity.Property(e => e.IsRecipe).HasColumnName("IsRecipe");
            entity.Property(e => e.IsExpirable).HasColumnName("IsExpire");
            entity.Property(e => e.IsProduction).HasColumnName("IsProduction");
            entity.Property(e => e.IsSales).HasColumnName("IsSales");
            entity.Property(e => e.AuthorizationLevel).HasColumnName("Auth");
            entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
            entity.Property(e => e.ModifiedDate).HasColumnName("ModifiedDate");

            // Relationships
            entity.HasOne(e => e.Recipe)
                  .WithOne(r => r.Product)
                  .HasForeignKey<Recipe>(r => r.ProductId);
        });

        // Configure Recipe entity to match VB.NET structure
        // CRITICAL: Recipe shares table with RecipeIngredient, Recipe_Product_Id=NULL for headers
        modelBuilder.Entity<Recipe>(entity =>
        {
            entity.HasKey(e => e.RecipeId);
            entity.Property(e => e.RecipeId).HasColumnName("Recipe_Id");
            entity.Property(e => e.ProductId).HasColumnName("Product_Id");
            entity.Property(e => e.RecipeName).HasColumnName("Recipe_Name").HasMaxLength(200);
            entity.Property(e => e.Description).HasColumnName("Description").HasMaxLength(500);
            entity.Property(e => e.Version).HasColumnName("Version").HasMaxLength(20);
            entity.Property(e => e.OutputQuantity).HasColumnName("Output_Quantity").HasColumnType("decimal(18,4)");
            entity.Property(e => e.OutputUnit).HasColumnName("Output_Unit").HasMaxLength(50);
            entity.Property(e => e.EstimatedProductionTimeMinutes).HasColumnName("Production_Time_Minutes");
            entity.Property(e => e.LaborCost).HasColumnName("Labor_Cost").HasColumnType("decimal(18,4)");
            entity.Property(e => e.OverheadCost).HasColumnName("Overhead_Cost").HasColumnType("decimal(18,4)");
            entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
            entity.Property(e => e.IsDefault).HasColumnName("IsDefault").HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
            entity.Property(e => e.ModifiedDate).HasColumnName("ModifiedDate");
            entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy");
            entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy");

            // CRITICAL: Add Recipe_Product_Id column mapping for shared table
            entity.Property<int?>("Recipe_Product_Id").HasColumnName("Recipe_Product_Id");

            // Query filter: Recipe headers have Recipe_Product_Id = NULL
            entity.HasQueryFilter(r => EF.Property<int?>(r, "Recipe_Product_Id") == null);

            // Relationships
            entity.HasMany(e => e.Ingredients)
                  .WithOne(i => i.Recipe)
                  .HasForeignKey(i => i.RecipeId);
        });

        // Configure RecipeIngredient entity to match VB.NET structure
        // CRITICAL: RecipeIngredient shares table with Recipe, Recipe_Product_Id!=NULL for ingredients
        // The ShowWithProduct_IdAll function: "SELECT * FROM Recipe_ProductsTbl where Product_Id=" & Product_Id
        modelBuilder.Entity<RecipeIngredient>(entity =>
        {
            entity.HasKey(e => e.RecipeIngredientId);
            entity.Property(e => e.RecipeIngredientId).HasColumnName("Recipe_Id"); // Primary key in VB.NET
            entity.Property(e => e.RecipeId).HasColumnName("Product_Id"); // Main product ID
            entity.Property(e => e.IngredientProductId).HasColumnName("Recipe_Product_Id"); // Ingredient product ID
            entity.Property(e => e.RequiredQuantity).HasColumnName("UsedQuantity").HasColumnType("decimal(18,4)");
            entity.Property(e => e.RequiredUnit).HasColumnName("Required_Unit").HasMaxLength(50);
            entity.Property(e => e.CostPerUnit).HasColumnName("Recipe_Lost_cost").HasColumnType("decimal(18,4)");
            entity.Property(e => e.SequenceOrder).HasColumnName("Sequence_Order");
            entity.Property(e => e.Notes).HasColumnName("Notes").HasMaxLength(500);
            entity.Property(e => e.IsOptional).HasColumnName("IsOptional").HasDefaultValue(false);
            entity.Property(e => e.WastePercentage).HasColumnName("Waste_Percentage").HasColumnType("decimal(5,2)");
            entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
            entity.Property(e => e.ModifiedDate).HasColumnName("ModifiedDate");

            // Query filter: Recipe ingredients have Recipe_Product_Id != NULL
            entity.HasQueryFilter(ri => ri.IngredientProductId != null);

            // Relationships
            entity.HasOne(e => e.IngredientProduct)
                  .WithMany()
                  .HasForeignKey(e => e.IngredientProductId);
        });

        // Configure Inventory entity to match VB.NET structure
        modelBuilder.Entity<Inventory>(entity =>
        {
            entity.HasKey(e => e.InventoryId);
            entity.Property(e => e.InventoryId).HasColumnName("Ser");
            entity.Property(e => e.ProductId).HasColumnName("Product_Id");
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenter_Id");
            entity.Property(e => e.CostCenterName).HasColumnName("CostCenter_Name").HasMaxLength(100);
            entity.Property(e => e.QuantityOnHand).HasColumnName("Quntity").HasColumnType("decimal(18,4)");
            entity.Property(e => e.QuantityReserved).HasColumnName("Reserved_Quantity").HasColumnType("decimal(18,4)");
            entity.Property(e => e.BaseUnitQuantity).HasColumnName("QuntityBase").HasColumnType("decimal(18,4)");
            entity.Property(e => e.UnitConversionFactor).HasColumnName("Item_Unit").HasColumnType("decimal(18,4)");
            entity.Property(e => e.AverageCost).HasColumnName("AvCost").HasColumnType("decimal(18,4)");
            entity.Property(e => e.LastCost).HasColumnName("Cost_Product").HasColumnType("decimal(18,4)");
            entity.Property(e => e.LastMovementDate).HasColumnName("LastMovementDate").HasDefaultValueSql("GETDATE()");
            entity.Property(e => e.LastCountDate).HasColumnName("LastCountDate");
            entity.Property(e => e.IsActive).HasColumnName("IsActive").HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
            entity.Property(e => e.ModifiedDate).HasColumnName("ModifiedDate");

            // Relationships
            entity.HasOne(e => e.Product)
                  .WithMany(p => p.InventoryRecords)
                  .HasForeignKey(e => e.ProductId);
        });

        // Configure ProductionOrder entity
        modelBuilder.Entity<ProductionOrder>(entity =>
        {
            entity.HasKey(e => e.ProductionOrderId);
            entity.Property(e => e.ProductionOrderId).HasColumnName("Production_Order_Id");
            entity.Property(e => e.OrderNumber).HasColumnName("Order_Number").HasMaxLength(50);
            entity.Property(e => e.ProductId).HasColumnName("Product_Id");
            entity.Property(e => e.RecipeId).HasColumnName("Recipe_Id");
            entity.Property(e => e.QuantityToProduce).HasColumnName("Quantity_To_Produce").HasColumnType("decimal(18,4)");
            entity.Property(e => e.QuantityProduced).HasColumnName("Quantity_Produced").HasColumnType("decimal(18,4)");
            entity.Property(e => e.Unit).HasColumnName("Unit").HasMaxLength(50);
            entity.Property(e => e.Status).HasColumnName("Status");
            entity.Property(e => e.Priority).HasColumnName("Priority");
            entity.Property(e => e.CostCenterId).HasColumnName("CostCenter_Id");
            entity.Property(e => e.CostCenterName).HasColumnName("CostCenter_Name").HasMaxLength(100);
            entity.Property(e => e.PlannedStartDate).HasColumnName("Planned_Start_Date");
            entity.Property(e => e.PlannedEndDate).HasColumnName("Planned_End_Date");
            entity.Property(e => e.ActualStartDate).HasColumnName("Actual_Start_Date");
            entity.Property(e => e.ActualEndDate).HasColumnName("Actual_End_Date");
            entity.Property(e => e.EstimatedCost).HasColumnName("Estimated_Cost").HasColumnType("decimal(18,4)");
            entity.Property(e => e.ActualCost).HasColumnName("Actual_Cost").HasColumnType("decimal(18,4)");
            entity.Property(e => e.Notes).HasColumnName("Notes").HasMaxLength(500);
            entity.Property(e => e.ParentProductionOrderId).HasColumnName("Parent_Production_Order_Id");
            entity.Property(e => e.HierarchyLevel).HasColumnName("Hierarchy_Level");
            entity.Property(e => e.IsAutoGenerated).HasColumnName("Is_Auto_Generated").HasDefaultValue(false);
            entity.Property(e => e.CreatedDate).HasColumnName("CreatedDate").HasDefaultValueSql("GETDATE()");
            entity.Property(e => e.ModifiedDate).HasColumnName("ModifiedDate");
            entity.Property(e => e.CreatedBy).HasColumnName("CreatedBy");
            entity.Property(e => e.ModifiedBy).HasColumnName("ModifiedBy");

            // Relationships
            entity.HasOne(e => e.Product)
                  .WithMany()
                  .HasForeignKey(e => e.ProductId);

            entity.HasOne(e => e.Recipe)
                  .WithMany()
                  .HasForeignKey(e => e.RecipeId);

            entity.HasOne(e => e.ParentProductionOrder)
                  .WithMany(p => p.ChildProductionOrders)
                  .HasForeignKey(e => e.ParentProductionOrderId);
        });

        // Configure POSSetting entity (in SCM database - CRITICAL DISCOVERY!)
        modelBuilder.Entity<POSSetting>(entity =>
        {
            entity.HasKey(e => e.POSSettingId);
            entity.Property(e => e.POSSettingId).HasColumnName("Ser");
            entity.Property(e => e.CompanyIdPOS).HasColumnName("Company_IdPOS");
            entity.Property(e => e.CompanyNamePOS).HasColumnName("Company_NamePOS").HasMaxLength(200);
            entity.Property(e => e.BrandIdPOS).HasColumnName("Brand_IdPOS");
            entity.Property(e => e.BrandNamePOS).HasColumnName("Brand_NamePOS").HasMaxLength(200);
            entity.Property(e => e.CostCenterIdPOS).HasColumnName("CostCenter_IdPOS");
            entity.Property(e => e.CostCenterNamePOS).HasColumnName("CostCenter_NamePOS").HasMaxLength(200);
            entity.Property(e => e.IsActive).HasColumnName("IsDelete").HasConversion(v => !v, v => !v); // IsDelete=0 means IsActive=true
        });

        // Configure SalesPOS entity (in SCM database - CRITICAL DISCOVERY!)
        modelBuilder.Entity<SalesPOS>(entity =>
        {
            entity.HasKey(e => e.SalesPOSId);
            entity.Property(e => e.SalesPOSId).HasColumnName("Ser");
            // All Sales_POS columns are in SCM database, not POS database
        });

        // Configure indexes for performance (matching VB.NET queries)
        modelBuilder.Entity<Product>()
            .HasIndex(e => e.ProductCode)
            .HasDatabaseName("IX_Products_ProductCode");

        modelBuilder.Entity<Product>()
            .HasIndex(e => new { e.IsRecipe, e.IsProduction })
            .HasDatabaseName("IX_Products_RecipeProduction");

        modelBuilder.Entity<RecipeIngredient>()
            .HasIndex(e => e.RecipeId)
            .HasDatabaseName("IX_RecipeIngredients_RecipeId");

        modelBuilder.Entity<Inventory>()
            .HasIndex(e => new { e.ProductId, e.CostCenterId })
            .HasDatabaseName("IX_Inventory_ProductCostCenter");

        modelBuilder.Entity<ProductionOrder>()
            .HasIndex(e => e.Status)
            .HasDatabaseName("IX_ProductionOrders_Status");

        modelBuilder.Entity<ProductionOrder>()
            .HasIndex(e => e.ParentProductionOrderId)
            .HasDatabaseName("IX_ProductionOrders_Parent");
    }
}
