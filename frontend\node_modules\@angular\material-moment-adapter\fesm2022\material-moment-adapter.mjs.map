{"version": 3, "file": "material-moment-adapter.mjs", "sources": ["../../../../../../src/material-moment-adapter/adapter/moment-date-adapter.ts", "../../../../../../src/material-moment-adapter/adapter/moment-date-formats.ts", "../../../../../../src/material-moment-adapter/adapter/index.ts", "../../../../../../src/material-moment-adapter/material-moment-adapter_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Inject, Injectable, Optional, InjectionToken} from '@angular/core';\nimport {DateAdapter, MAT_DATE_LOCALE} from '@angular/material/core';\n// Depending on whether rollup is used, moment needs to be imported differently.\n// Since Moment.js doesn't have a default export, we normally need to import using the `* as`\n// syntax. However, rollup creates a synthetic default module and we thus need to import it using\n// the `default as` syntax.\n// TODO(mmalerba): See if we can clean this up at some point.\nimport * as _moment from 'moment';\n// tslint:disable-next-line:no-duplicate-imports\nimport {default as _rollupMoment, Moment, MomentFormatSpecification, MomentInput} from 'moment';\n\nconst moment = _rollupMoment || _moment;\n\n/** Configurable options for MomentDateAdapter. */\nexport interface MatMomentDateAdapterOptions {\n  /**\n   * When enabled, the dates have to match the format exactly.\n   * See https://momentjs.com/guides/#/parsing/strict-mode/.\n   */\n  strict?: boolean;\n\n  /**\n   * Turns the use of utc dates on or off.\n   * Changing this will change how Angular Material components like DatePicker output dates.\n   * Defaults to `false`.\n   */\n  useUtc?: boolean;\n}\n\n/** InjectionToken for moment date adapter to configure options. */\nexport const MAT_MOMENT_DATE_ADAPTER_OPTIONS = new InjectionToken<MatMomentDateAdapterOptions>(\n  'MAT_MOMENT_DATE_ADAPTER_OPTIONS',\n  {\n    providedIn: 'root',\n    factory: MAT_MOMENT_DATE_ADAPTER_OPTIONS_FACTORY,\n  },\n);\n\n/** @docs-private */\nexport function MAT_MOMENT_DATE_ADAPTER_OPTIONS_FACTORY(): MatMomentDateAdapterOptions {\n  return {\n    useUtc: false,\n  };\n}\n\n/** Creates an array and fills it with values. */\nfunction range<T>(length: number, valueFunction: (index: number) => T): T[] {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n\n/** Adapts Moment.js Dates for use with Angular Material. */\n@Injectable()\nexport class MomentDateAdapter extends DateAdapter<Moment> {\n  // Note: all of the methods that accept a `Moment` input parameter immediately call `this.clone`\n  // on it. This is to ensure that we're working with a `Moment` that has the correct locale setting\n  // while avoiding mutating the original object passed to us. Just calling `.locale(...)` on the\n  // input would mutate the object.\n\n  private _localeData: {\n    firstDayOfWeek: number;\n    longMonths: string[];\n    shortMonths: string[];\n    dates: string[];\n    longDaysOfWeek: string[];\n    shortDaysOfWeek: string[];\n    narrowDaysOfWeek: string[];\n  };\n\n  constructor(\n    @Optional() @Inject(MAT_DATE_LOCALE) dateLocale: string,\n    @Optional()\n    @Inject(MAT_MOMENT_DATE_ADAPTER_OPTIONS)\n    private _options?: MatMomentDateAdapterOptions,\n  ) {\n    super();\n    this.setLocale(dateLocale || moment.locale());\n  }\n\n  override setLocale(locale: string) {\n    super.setLocale(locale);\n\n    let momentLocaleData = moment.localeData(locale);\n    this._localeData = {\n      firstDayOfWeek: momentLocaleData.firstDayOfWeek(),\n      longMonths: momentLocaleData.months(),\n      shortMonths: momentLocaleData.monthsShort(),\n      dates: range(31, i => this.createDate(2017, 0, i + 1).format('D')),\n      longDaysOfWeek: momentLocaleData.weekdays(),\n      shortDaysOfWeek: momentLocaleData.weekdaysShort(),\n      narrowDaysOfWeek: momentLocaleData.weekdaysMin(),\n    };\n  }\n\n  getYear(date: Moment): number {\n    return this.clone(date).year();\n  }\n\n  getMonth(date: Moment): number {\n    return this.clone(date).month();\n  }\n\n  getDate(date: Moment): number {\n    return this.clone(date).date();\n  }\n\n  getDayOfWeek(date: Moment): number {\n    return this.clone(date).day();\n  }\n\n  getMonthNames(style: 'long' | 'short' | 'narrow'): string[] {\n    // Moment.js doesn't support narrow month names, so we just use short if narrow is requested.\n    return style == 'long' ? this._localeData.longMonths : this._localeData.shortMonths;\n  }\n\n  getDateNames(): string[] {\n    return this._localeData.dates;\n  }\n\n  getDayOfWeekNames(style: 'long' | 'short' | 'narrow'): string[] {\n    if (style == 'long') {\n      return this._localeData.longDaysOfWeek;\n    }\n    if (style == 'short') {\n      return this._localeData.shortDaysOfWeek;\n    }\n    return this._localeData.narrowDaysOfWeek;\n  }\n\n  getYearName(date: Moment): string {\n    return this.clone(date).format('YYYY');\n  }\n\n  getFirstDayOfWeek(): number {\n    return this._localeData.firstDayOfWeek;\n  }\n\n  getNumDaysInMonth(date: Moment): number {\n    return this.clone(date).daysInMonth();\n  }\n\n  clone(date: Moment): Moment {\n    return date.clone().locale(this.locale);\n  }\n\n  createDate(year: number, month: number, date: number): Moment {\n    // Moment.js will create an invalid date if any of the components are out of bounds, but we\n    // explicitly check each case so we can throw more descriptive errors.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n\n    const result = this._createMoment({year, month, date}).locale(this.locale);\n\n    // If the result isn't valid, the date must have been out of bounds for this month.\n    if (!result.isValid() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n\n    return result;\n  }\n\n  today(): Moment {\n    return this._createMoment().locale(this.locale);\n  }\n\n  parse(value: any, parseFormat: string | string[]): Moment | null {\n    if (value && typeof value == 'string') {\n      return this._createMoment(value, parseFormat, this.locale);\n    }\n    return value ? this._createMoment(value).locale(this.locale) : null;\n  }\n\n  format(date: Moment, displayFormat: string): string {\n    date = this.clone(date);\n    if (!this.isValid(date) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('MomentDateAdapter: Cannot format invalid date.');\n    }\n    return date.format(displayFormat);\n  }\n\n  addCalendarYears(date: Moment, years: number): Moment {\n    return this.clone(date).add({years});\n  }\n\n  addCalendarMonths(date: Moment, months: number): Moment {\n    return this.clone(date).add({months});\n  }\n\n  addCalendarDays(date: Moment, days: number): Moment {\n    return this.clone(date).add({days});\n  }\n\n  toIso8601(date: Moment): string {\n    return this.clone(date).format();\n  }\n\n  /**\n   * Returns the given value if given a valid Moment or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) and valid Date objects into valid Moments and empty\n   * string into null. Returns an invalid date for all other values.\n   */\n  override deserialize(value: any): Moment | null {\n    let date;\n    if (value instanceof Date) {\n      date = this._createMoment(value).locale(this.locale);\n    } else if (this.isDateInstance(value)) {\n      // Note: assumes that cloning also sets the correct locale.\n      return this.clone(value);\n    }\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      date = this._createMoment(value, moment.ISO_8601).locale(this.locale);\n    }\n    if (date && this.isValid(date)) {\n      return this._createMoment(date).locale(this.locale);\n    }\n    return super.deserialize(value);\n  }\n\n  isDateInstance(obj: any): boolean {\n    return moment.isMoment(obj);\n  }\n\n  isValid(date: Moment): boolean {\n    return this.clone(date).isValid();\n  }\n\n  invalid(): Moment {\n    return moment.invalid();\n  }\n\n  /** Creates a Moment instance while respecting the current UTC settings. */\n  private _createMoment(\n    date?: MomentInput,\n    format?: MomentFormatSpecification,\n    locale?: string,\n  ): Moment {\n    const {strict, useUtc}: MatMomentDateAdapterOptions = this._options || {};\n\n    return useUtc ? moment.utc(date, format, locale, strict) : moment(date, format, locale, strict);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {MatDateFormats} from '@angular/material/core';\n\nexport const MAT_MOMENT_DATE_FORMATS: MatDateFormats = {\n  parse: {\n    dateInput: 'l',\n  },\n  display: {\n    dateInput: 'l',\n    monthYearLabel: 'MMM YYYY',\n    dateA11yLabel: 'LL',\n    monthYearA11yLabel: 'MMMM YYYY',\n  },\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule, Provider} from '@angular/core';\nimport {\n  DateAdapter,\n  MAT_DATE_FORMATS,\n  MAT_DATE_LOCALE,\n  MatDateFormats,\n} from '@angular/material/core';\nimport {\n  MAT_MOMENT_DATE_ADAPTER_OPTIONS,\n  MatMomentDateAdapterOptions,\n  MomentDateAdapter,\n} from './moment-date-adapter';\nimport {MAT_MOMENT_DATE_FORMATS} from './moment-date-formats';\n\nexport * from './moment-date-adapter';\nexport * from './moment-date-formats';\n\n@NgModule({\n  providers: [\n    {\n      provide: DateAdapter,\n      useClass: MomentDateAdapter,\n      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],\n    },\n  ],\n})\nexport class MomentDateModule {}\n\n@NgModule({\n  providers: [provideMomentDateAdapter()],\n})\nexport class MatMomentDateModule {}\n\nexport function provideMomentDateAdapter(\n  formats: MatDateFormats = MAT_MOMENT_DATE_FORMATS,\n  options?: MatMomentDateAdapterOptions,\n): Provider[] {\n  const providers: Provider[] = [\n    {\n      provide: DateAdapter,\n      useClass: MomentDateAdapter,\n      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],\n    },\n    {provide: MAT_DATE_FORMATS, useValue: formats},\n  ];\n\n  if (options) {\n    providers.push({provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: options});\n  }\n\n  return providers;\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["_rollupMoment", "_moment"], "mappings": ";;;;;;AAmBA,MAAM,MAAM,GAAGA,sBAAa,IAAIC,aAAO,CAAC;AAkBxC;MACa,+BAA+B,GAAG,IAAI,cAAc,CAC/D,iCAAiC,EACjC;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,uCAAuC;AACjD,CAAA,EACD;AAEF;SACgB,uCAAuC,GAAA;IACrD,OAAO;AACL,QAAA,MAAM,EAAE,KAAK;KACd,CAAC;AACJ,CAAC;AAED;AACA,SAAS,KAAK,CAAI,MAAc,EAAE,aAAmC,EAAA;AACnE,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAClC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,WAAW,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;KACnC;AACD,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;AAEM,MAAO,iBAAkB,SAAQ,WAAmB,CAAA;IAgBxD,WACuC,CAAA,UAAkB,EAG/C,QAAsC,EAAA;AAE9C,QAAA,KAAK,EAAE,CAAC;QAFA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA8B;QAG9C,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;KAC/C;AAEQ,IAAA,SAAS,CAAC,MAAc,EAAA;AAC/B,QAAA,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAExB,IAAI,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG;AACjB,YAAA,cAAc,EAAE,gBAAgB,CAAC,cAAc,EAAE;AACjD,YAAA,UAAU,EAAE,gBAAgB,CAAC,MAAM,EAAE;AACrC,YAAA,WAAW,EAAE,gBAAgB,CAAC,WAAW,EAAE;YAC3C,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAClE,YAAA,cAAc,EAAE,gBAAgB,CAAC,QAAQ,EAAE;AAC3C,YAAA,eAAe,EAAE,gBAAgB,CAAC,aAAa,EAAE;AACjD,YAAA,gBAAgB,EAAE,gBAAgB,CAAC,WAAW,EAAE;SACjD,CAAC;KACH;AAED,IAAA,OAAO,CAAC,IAAY,EAAA;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;KAChC;AAED,IAAA,QAAQ,CAAC,IAAY,EAAA;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;KACjC;AAED,IAAA,OAAO,CAAC,IAAY,EAAA;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;KAChC;AAED,IAAA,YAAY,CAAC,IAAY,EAAA;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;KAC/B;AAED,IAAA,aAAa,CAAC,KAAkC,EAAA;;AAE9C,QAAA,OAAO,KAAK,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;KACrF;IAED,YAAY,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;KAC/B;AAED,IAAA,iBAAiB,CAAC,KAAkC,EAAA;AAClD,QAAA,IAAI,KAAK,IAAI,MAAM,EAAE;AACnB,YAAA,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;SACxC;AACD,QAAA,IAAI,KAAK,IAAI,OAAO,EAAE;AACpB,YAAA,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;SACzC;AACD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC;KAC1C;AAED,IAAA,WAAW,CAAC,IAAY,EAAA;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KACxC;IAED,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;KACxC;AAED,IAAA,iBAAiB,CAAC,IAAY,EAAA;QAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;KACvC;AAED,IAAA,KAAK,CAAC,IAAY,EAAA;QAChB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzC;AAED,IAAA,UAAU,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAA;;;AAGlD,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE;AAC3B,gBAAA,MAAM,KAAK,CAAC,CAAA,qBAAA,EAAwB,KAAK,CAAA,0CAAA,CAA4C,CAAC,CAAC;aACxF;AAED,YAAA,IAAI,IAAI,GAAG,CAAC,EAAE;AACZ,gBAAA,MAAM,KAAK,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAA,iCAAA,CAAmC,CAAC,CAAC;aACvE;SACF;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,EAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;AAG3E,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACxE,MAAM,KAAK,CAAC,CAAiB,cAAA,EAAA,IAAI,2BAA2B,KAAK,CAAA,EAAA,CAAI,CAAC,CAAC;SACxE;AAED,QAAA,OAAO,MAAM,CAAC;KACf;IAED,KAAK,GAAA;QACH,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACjD;IAED,KAAK,CAAC,KAAU,EAAE,WAA8B,EAAA;AAC9C,QAAA,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAC5D;QACD,OAAO,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;KACrE;IAED,MAAM,CAAC,IAAY,EAAE,aAAqB,EAAA;AACxC,QAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC1E,YAAA,MAAM,KAAK,CAAC,gDAAgD,CAAC,CAAC;SAC/D;AACD,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;KACnC;IAED,gBAAgB,CAAC,IAAY,EAAE,KAAa,EAAA;AAC1C,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC;KACtC;IAED,iBAAiB,CAAC,IAAY,EAAE,MAAc,EAAA;AAC5C,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAC,MAAM,EAAC,CAAC,CAAC;KACvC;IAED,eAAe,CAAC,IAAY,EAAE,IAAY,EAAA;AACxC,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAC,IAAI,EAAC,CAAC,CAAC;KACrC;AAED,IAAA,SAAS,CAAC,IAAY,EAAA;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;KAClC;AAED;;;;AAIG;AACM,IAAA,WAAW,CAAC,KAAU,EAAA;AAC7B,QAAA,IAAI,IAAI,CAAC;AACT,QAAA,IAAI,KAAK,YAAY,IAAI,EAAE;AACzB,YAAA,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtD;AAAM,aAAA,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;;AAErC,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAC1B;AACD,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,KAAK,EAAE;AACV,gBAAA,OAAO,IAAI,CAAC;aACb;AACD,YAAA,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACvE;QACD,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACrD;AACD,QAAA,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACjC;AAED,IAAA,cAAc,CAAC,GAAQ,EAAA;AACrB,QAAA,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;KAC7B;AAED,IAAA,OAAO,CAAC,IAAY,EAAA;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;KACnC;IAED,OAAO,GAAA;AACL,QAAA,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC;KACzB;;AAGO,IAAA,aAAa,CACnB,IAAkB,EAClB,MAAkC,EAClC,MAAe,EAAA;QAEf,MAAM,EAAC,MAAM,EAAE,MAAM,EAAC,GAAgC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;AAE1E,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;KACjG;8GApMU,iBAAiB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAiBN,eAAe,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAE3B,+BAA+B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;kHAnB9B,iBAAiB,EAAA,CAAA,CAAA,EAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;;0BAkBN,QAAQ;;0BAAI,MAAM;2BAAC,eAAe,CAAA;;0BAClC,QAAQ;;0BACR,MAAM;2BAAC,+BAA+B,CAAA;;;ACzE9B,MAAA,uBAAuB,GAAmB;AACrD,IAAA,KAAK,EAAE;AACL,QAAA,SAAS,EAAE,GAAG;AACf,KAAA;AACD,IAAA,OAAO,EAAE;AACP,QAAA,SAAS,EAAE,GAAG;AACd,QAAA,cAAc,EAAE,UAAU;AAC1B,QAAA,aAAa,EAAE,IAAI;AACnB,QAAA,kBAAkB,EAAE,WAAW;AAChC,KAAA;;;MCeU,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAhB,gBAAgB,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EARhB,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,WAAW;AACpB,gBAAA,QAAQ,EAAE,iBAAiB;AAC3B,gBAAA,IAAI,EAAE,CAAC,eAAe,EAAE,+BAA+B,CAAC;AACzD,aAAA;AACF,SAAA,EAAA,CAAA,CAAA,EAAA;;2FAEU,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAT5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,WAAW;AACpB,4BAAA,QAAQ,EAAE,iBAAiB;AAC3B,4BAAA,IAAI,EAAE,CAAC,eAAe,EAAE,+BAA+B,CAAC;AACzD,yBAAA;AACF,qBAAA;AACF,iBAAA,CAAA;;MAMY,mBAAmB,CAAA;8GAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAnB,mBAAmB,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EAFnB,SAAA,EAAA,CAAC,wBAAwB,EAAE,CAAC,EAAA,CAAA,CAAA,EAAA;;2FAE5B,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAH/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE,CAAC,wBAAwB,EAAE,CAAC;AACxC,iBAAA,CAAA;;SAGe,wBAAwB,CACtC,OAA0B,GAAA,uBAAuB,EACjD,OAAqC,EAAA;AAErC,IAAA,MAAM,SAAS,GAAe;AAC5B,QAAA;AACE,YAAA,OAAO,EAAE,WAAW;AACpB,YAAA,QAAQ,EAAE,iBAAiB;AAC3B,YAAA,IAAI,EAAE,CAAC,eAAe,EAAE,+BAA+B,CAAC;AACzD,SAAA;AACD,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAC;KAC/C,CAAC;IAEF,IAAI,OAAO,EAAE;AACX,QAAA,SAAS,CAAC,IAAI,CAAC,EAAC,OAAO,EAAE,+BAA+B,EAAE,QAAQ,EAAE,OAAO,EAAC,CAAC,CAAC;KAC/E;AAED,IAAA,OAAO,SAAS,CAAC;AACnB;;AC3DA;;AAEG;;;;"}