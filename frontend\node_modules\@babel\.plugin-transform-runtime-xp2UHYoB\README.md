# @babel/plugin-transform-runtime

> Externalise references to helpers and builtins, automatically polyfilling your code without polluting globals

See our website [@babel/plugin-transform-runtime](https://babeljs.io/docs/babel-plugin-transform-runtime) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-runtime
```

or using yarn:

```sh
yarn add @babel/plugin-transform-runtime --dev
```
