using Microsoft.EntityFrameworkCore;
using IFC.Domain.Entities.POS;

namespace IFC.Infrastructure.Data;

/// <summary>
/// Entity Framework DbContext for POS Database
/// Replicates VB.NET SqlConnPOS connection and operations
/// </summary>
public class POSDbContext : DbContext
{
    public POSDbContext(DbContextOptions<POSDbContext> options) : base(options)
    {
    }

    // POS entities (only entities that are actually in POS database)
    // NOTE: POSSetting and SalesPOS are in SCM database, not POS database!
    public DbSet<POSTransaction> POSTransactions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // NOTE: POSSetting and SalesPOS configurations removed - they are in SCM database!

        // Configure POSTransaction entity (view)
        modelBuilder.Entity<POSTransaction>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToView("v_ProcIFC"); // This is a view, not a table

            // Indexes for performance (if supported by view)
            entity.HasIndex(e => new { e.<PERSON>, e.Outlet, e.Center })
                  .HasDatabaseName("IX_POSTransaction_MandantOutletCenter");

            entity.HasIndex(e => e.StatistDate)
                  .HasDatabaseName("IX_POSTransaction_StatistDate");

            entity.HasIndex(e => e.IsIFCDone)
                  .HasDatabaseName("IX_POSTransaction_IsIFCDone");
        });
    }
}
